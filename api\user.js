// api/user.js - 用户相关API接口
const { post } = require('../utils/request.js');
const { md5 } = require('../utils/crypto.js');
const {
  validatePhone,
  validatePassword,
  validateVerificationCode,
  validateNickname,
  validateNumberRange,
  validateArray,
  validateRequiredFields
} = require('../utils/validator.js');

/**
 * 发送验证码
 * @param {string} userName - 手机号
 * @param {number} type - 1-注册 2-登录&重置密码 3-微信授权绑定手机号
 * @returns {Promise} Promise对象
 */
function sendVerificationCode(userName, type) {
  // 参数验证
  if (!validatePhone(userName)) {
    return Promise.reject({ code: 1000, msg: '手机号格式错误' });
  }

  if (!validateNumberRange(type, 1, 3)) {
    return Promise.reject({ code: 1020, msg: '验证码类型错误，必须为1、2或3' });
  }

  const requestData = {
    Action: 'SEND_VERIFICATION_CODE',
    userName: userName,
    type: type
  };

  console.log('=== 发送验证码API请求 ===');
  console.log('请求URL: /api/user/action');
  console.log('请求参数:', JSON.stringify(requestData, null, 2));
  console.log('用户名:', userName);
  console.log('类型:', type, type === 1 ? '(注册)' : type === 2 ? '(登录&重置密码)' : type === 3 ? '(微信授权绑定)' : '(未知)');
  console.log('=== 发送验证码API请求结束 ===');

  return post('/api/user/action', requestData);
}

/**
 * 用户注册
 * @param {string} username - 用户名(手机号)
 * @param {string} password - 密码(原始密码，会自动MD5加密)
 * @param {string} verificationCode - 验证码
 * @param {string} nickname - 昵称(可选)
 * @returns {Promise} Promise对象
 */
function register(username, password, verificationCode, nickname = '') {
  // 参数验证
  if (!validatePhone(username)) {
    return Promise.reject({ code: 1000, msg: '手机号格式错误' });
  }

  const passwordValidation = validatePassword(password, { minLength: 6, maxLength: 20 });
  if (!passwordValidation.valid) {
    return Promise.reject({ code: 1009, msg: passwordValidation.message });
  }

  if (!validateVerificationCode(verificationCode)) {
    return Promise.reject({ code: 1005, msg: '验证码格式错误' });
  }

  if (nickname) {
    const nicknameValidation = validateNickname(nickname);
    if (!nicknameValidation.valid) {
      return Promise.reject({ code: 1046, msg: nicknameValidation.message });
    }
  }

  const encryptedPassword = md5(password);
  const requestData = {
    Action: 'REGISTER_USER',
    userName: username,
    password: encryptedPassword, // MD5加密
    verificationCode: verificationCode,
    nickname: nickname
  };

  console.log('=== 用户注册API请求 ===');
  console.log('请求URL: /api/user/action');
  console.log('用户名:', username);
  console.log('原始密码长度:', password.length);
  console.log('MD5加密后密码:', encryptedPassword);
  console.log('验证码:', verificationCode);
  console.log('昵称:', nickname || '(空)');
  console.log('完整请求参数:', JSON.stringify(requestData, null, 2));
  console.log('=== 用户注册API请求结束 ===');

  return post('/api/user/action', requestData);
}

/**
 * 密码登录
 * @param {string} username - 用户名(手机号)
 * @param {string} password - 密码(原始密码，会自动MD5加密)
 * @returns {Promise} Promise对象
 */
function loginWithPassword(username, password) {
  // 参数验证
  if (!validatePhone(username)) {
    return Promise.reject({ code: 1000, msg: '手机号格式错误' });
  }

  if (!password || typeof password !== 'string' || password.length === 0) {
    return Promise.reject({ code: 1009, msg: '密码不能为空' });
  }

  return post('/api/user/action', {
    Action: 'LOGIN_PASSWORD',
    userName: username,
    password: md5(password) // MD5加密
  });
}

/**
 * 验证码登录
 * @param {string} username - 用户名(手机号)
 * @param {string} code - 验证码
 * @returns {Promise} Promise对象
 */
function loginWithCode(username, code) {
  // 参数验证
  if (!validatePhone(username)) {
    return Promise.reject({ code: 1000, msg: '手机号格式错误' });
  }

  if (!validateVerificationCode(code)) {
    return Promise.reject({ code: 1005, msg: '验证码格式错误' });
  }

  return post('/api/user/action', {
    Action: 'LOGIN_VERIFICATION_CODE',
    userName: username,
    code: code
  });
}

/**
 * 一键登录
 * @param {string} accessToken - 运营商访问token
 * @returns {Promise} Promise对象
 */
function oneKeyLogin(accessToken) {
  // 参数验证
  if (!accessToken || typeof accessToken !== 'string' || accessToken.trim().length === 0) {
    return Promise.reject({ code: 1026, msg: '一键登录token不能为空' });
  }

  return post('/api/user/action', {
    Action: 'ONE_KEY_LOGIN',
    accessToken: accessToken
  });
}

/**
 * 微信授权码操作
 * @param {string} code - 微信提供的授权码
 * @returns {Promise} Promise对象
 */
function getWechatAuth(code) {
  // 参数验证
  if (!code || typeof code !== 'string' || code.trim().length === 0) {
    return Promise.reject({ code: 1002, msg: '微信授权码不能为空' });
  }

  return post('/api/user/action', {
    Action: 'GET_WECHAT_AUTH',
    code: code
  }, {
    needToken: false  // 明确指定不需要Token
  });
}

/**
 * 小程序微信账号登录
 * @param {string} mobileCode - 授权CODE
 * @param {string} openIdCode - openIdCode(可选)
 * @returns {Promise} Promise对象
 */
function loginByWechatApplet(mobileCode, openIdCode = '') {
  // 参数验证
  if (!mobileCode || typeof mobileCode !== 'string' || mobileCode.trim().length === 0) {
    return Promise.reject({ code: 1002, msg: '微信授权码不能为空' });
  }

  if (openIdCode && (typeof openIdCode !== 'string' || openIdCode.trim().length === 0)) {
    return Promise.reject({ code: 1017, msg: 'OpenId码格式错误' });
  }

  const data = {
    Action: 'LOGIN_BY_WECHAT_APPLET',
    mobileCode: mobileCode
  };

  if (openIdCode) {
    data.openIdCode = openIdCode;
  }

  console.log('小程序微信账号登录参数:', data);
  console.log('mobileCode长度:', mobileCode.length, '格式:', typeof mobileCode);

  return post('/api/user/action', data, {
    needToken: false  // 明确指定不需要Token
  });
}

/**
 * 获取微信小程序用户openId信息
 * @param {string} code - 微信小程序授权code
 * @param {string} productKey - productKey
 * @param {string} deviceName - deviceName
 * @param {string} nickName - 用户昵称
 * @returns {Promise} Promise对象
 */
function getAppletOpenidInfo(code, productKey, deviceName, nickName) {
  // 参数验证
  if (!code || typeof code !== 'string' || code.trim().length === 0) {
    return Promise.reject({ code: 1002, msg: '微信授权码不能为空' });
  }

  if (!productKey || typeof productKey !== 'string' || productKey.trim().length === 0) {
    return Promise.reject({ code: 1012, msg: 'productKey不能为空' });
  }

  if (!deviceName || typeof deviceName !== 'string' || deviceName.trim().length === 0) {
    return Promise.reject({ code: 1012, msg: 'deviceName不能为空' });
  }

  if (nickName && typeof nickName !== 'string') {
    return Promise.reject({ code: 1046, msg: '昵称格式错误' });
  }

  return post('/api/user/action', {
    Action: 'GET_APPLET_OPENID_INFO',
    code: code,
    productKey: productKey,
    deviceName: deviceName,
    nickName: nickName
  }, {
    needToken: false  // 明确指定不需要Token
  });
}

/**
 * 微信OpenID绑定手机号
 * @param {string} userName - 用户名(手机号)
 * @param {string} openId - 微信openId
 * @param {string} verificationCode - 验证码
 * @returns {Promise} Promise对象
 */
function setWechatPhone(userName, openId, verificationCode) {
  // 参数验证
  if (!validatePhone(userName)) {
    return Promise.reject({ code: 1000, msg: '手机号格式错误' });
  }

  if (!openId || typeof openId !== 'string' || openId.trim().length === 0) {
    return Promise.reject({ code: 1017, msg: '微信OpenId不能为空' });
  }

  if (!validateVerificationCode(verificationCode)) {
    return Promise.reject({ code: 1005, msg: '验证码格式错误' });
  }

  return post('/api/user/action', {
    Action: 'SET_WECHAT_PHONE',
    userName: userName,
    openId: openId,
    verificationCode: verificationCode
  }, {
    needToken: false  // 明确指定不需要Token
  });
}

/**
 * 用户退出登录
 * @returns {Promise} Promise对象
 */
function logout() {
  const requestData = {
    Action: 'LOGOUT'
  };

  console.log('=== 用户退出登录API请求 ===');
  console.log('请求URL: /api/user/action');
  console.log('请求参数:', JSON.stringify(requestData, null, 2));
  console.log('=== 用户退出登录API请求结束 ===');

  return post('/api/user/action', requestData, {
    needToken: true  // 退出登录需要Token
  });
}

/**
 * 用户重置密码
 * @param {string} userName - 用户名(手机号)
 * @param {string} newPassword - 新密码(原始密码，会自动MD5加密)
 * @param {string} verificationCode - 验证码
 * @returns {Promise} Promise对象
 */
function resetPassword(userName, newPassword, verificationCode) {
  // 参数验证
  if (!validatePhone(userName)) {
    return Promise.reject({ code: 1000, msg: '手机号格式错误' });
  }

  const passwordValidation = validatePassword(newPassword, { minLength: 6, maxLength: 20 });
  if (!passwordValidation.valid) {
    return Promise.reject({ code: 1009, msg: passwordValidation.message });
  }

  if (!validateVerificationCode(verificationCode)) {
    return Promise.reject({ code: 1005, msg: '验证码格式错误' });
  }

  const encryptedPassword = md5(newPassword);
  const requestData = {
    Action: 'RESET_PASSWORD',
    userName: userName,
    newPassword: encryptedPassword, // MD5加密
    verificationCode: verificationCode
  };

  console.log('=== 用户重置密码API请求 ===');
  console.log('请求URL: /api/user/action');
  console.log('用户名:', userName);
  console.log('原始密码长度:', newPassword.length);
  console.log('MD5加密后密码:', encryptedPassword);
  console.log('验证码:', verificationCode);
  console.log('完整请求参数:', JSON.stringify(requestData, null, 2));
  console.log('=== 用户重置密码API请求结束 ===');

  return post('/api/user/action', requestData, {
    needToken: false  // 重置密码时可能没有Token
  });
}

/**
 * 获取用户信息
 * @returns {Promise} Promise对象
 */
function getUserInfo() {
  const requestData = {
    Action: 'GET_USER_INFO'
  };

  console.log('=== 获取用户信息API请求 ===');
  console.log('请求URL: /api/user/action');
  console.log('请求参数:', JSON.stringify(requestData, null, 2));
  console.log('=== 获取用户信息API请求结束 ===');

  return post('/api/user/action', requestData, {
    needToken: true  // 获取用户信息需要Token
  });
}

/**
 * 更新用户信息
 * @param {Object} userInfo - 用户信息对象
 * @param {string} userInfo.nickname - 昵称(可选)
 * @param {string} userInfo.avatarUrl - 头像URL(可选)
 * @param {string} userInfo.gender - 性别(可选)
 * @param {string} userInfo.birthday - 生日(可选)
 * @param {string} userInfo.location - 位置(可选)
 * @returns {Promise} Promise对象
 */
function updateUserInfo(userInfo) {
  // 参数验证
  if (!userInfo || typeof userInfo !== 'object') {
    return Promise.reject({ code: 1012, msg: '用户信息不能为空' });
  }

  // 验证昵称
  if (userInfo.nickname !== undefined) {
    const nicknameValidation = validateNickname(userInfo.nickname);
    if (!nicknameValidation.valid) {
      return Promise.reject({ code: 1046, msg: nicknameValidation.message });
    }
  }

  // 验证头像URL
  if (userInfo.avatarUrl !== undefined && userInfo.avatarUrl !== null && userInfo.avatarUrl !== '') {
    if (typeof userInfo.avatarUrl !== 'string' || !validateUrl(userInfo.avatarUrl)) {
      return Promise.reject({ code: 1022, msg: '头像URL格式错误' });
    }
  }

  // 验证性别
  if (userInfo.gender !== undefined && userInfo.gender !== null && userInfo.gender !== '') {
    if (!['male', 'female', '男', '女', '0', '1', '2'].includes(String(userInfo.gender))) {
      return Promise.reject({ code: 1012, msg: '性别参数错误' });
    }
  }

  const requestData = {
    Action: 'UPDATE_USER_INFO',
    ...userInfo
  };

  console.log('=== 更新用户信息API请求 ===');
  console.log('请求URL: /api/user/action');
  console.log('更新信息:', JSON.stringify(userInfo, null, 2));
  console.log('完整请求参数:', JSON.stringify(requestData, null, 2));
  console.log('=== 更新用户信息API请求结束 ===');

  return post('/api/user/action', requestData, {
    needToken: true  // 更新用户信息需要Token
  });
}

/**
 * 查询用户推送开关
 * @returns {Promise} Promise对象
 */
function getUserPushSwitch() {
  const requestData = {
    Action: 'GET_USER_PUSH_SWITCH'
  };

  console.log('=== 查询用户推送开关API请求 ===');
  console.log('请求URL: /api/user/action');
  console.log('请求参数:', JSON.stringify(requestData, null, 2));
  console.log('=== 查询用户推送开关API请求结束 ===');

  return post('/api/user/action', requestData, {
    needToken: true  // 查询推送开关需要Token
  });
}

/**
 * 设置用户推送开关
 * @param {boolean} enabled - 是否启用推送
 * @returns {Promise} Promise对象
 */
function setUserPushSwitch(enabled) {
  // 参数验证
  if (typeof enabled !== 'boolean') {
    return Promise.reject({ code: 1012, msg: '推送开关状态必须为布尔值' });
  }

  const requestData = {
    Action: 'SET_USER_PUSH_SWITCH',
    enabled: enabled
  };

  console.log('=== 设置用户推送开关API请求 ===');
  console.log('请求URL: /api/user/action');
  console.log('启用状态:', enabled);
  console.log('请求参数:', JSON.stringify(requestData, null, 2));
  console.log('=== 设置用户推送开关API请求结束 ===');

  return post('/api/user/action', requestData, {
    needToken: true  // 设置推送开关需要Token
  });
}

/**
 * 查询用户消息列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码，从1开始
 * @param {number} params.pageSize - 每页条数
 * @param {string} params.startTime - 开始时间(可选)
 * @param {string} params.endTime - 结束时间(可选)
 * @param {number} params.readStatus - 读取状态(可选): 0-全部, 1-未读, 2-已读
 * @returns {Promise} Promise对象
 */
function getUserSysMessageList(params = {}) {
  // 参数验证
  if (params.page !== undefined && (!validateNumberRange(params.page, 1, 10000))) {
    return Promise.reject({ code: 1012, msg: '页码必须为1-10000之间的正整数' });
  }

  if (params.pageSize !== undefined && (!validateNumberRange(params.pageSize, 1, 100))) {
    return Promise.reject({ code: 1012, msg: '每页条数必须为1-100之间的正整数' });
  }

  if (params.readStatus !== undefined && (!validateNumberRange(params.readStatus, 0, 2))) {
    return Promise.reject({ code: 1012, msg: '读取状态必须为0、1或2' });
  }

  const requestData = {
    Action: 'GET_USER_SYS_MESSAGE_LIST',
    page: params.page || 1,
    pageSize: params.pageSize || 10
  };

  // 添加可选参数
  if (params.startTime) requestData.startTime = params.startTime;
  if (params.endTime) requestData.endTime = params.endTime;
  if (params.readStatus !== undefined) requestData.readStatus = params.readStatus;

  console.log('=== 查询用户消息列表API请求 ===');
  console.log('请求URL: /api/user/action');
  console.log('请求参数:', JSON.stringify(requestData, null, 2));
  console.log('=== 查询用户消息列表API请求结束 ===');

  return post('/api/user/action', requestData, {
    needToken: true  // 查询用户消息需要Token
  });
}

/**
 * 更新消息状态为已读
 * @param {Array<string>} messageIds - 消息ID数组
 * @returns {Promise} Promise对象
 */
function batchUpdateMessageReadStatus(messageIds) {
  // 参数验证
  if (!validateArray(messageIds)) {
    return Promise.reject({ code: 1012, msg: '消息ID数组不能为空' });
  }

  if (messageIds.length > 100) {
    return Promise.reject({ code: 1057, msg: '批量操作消息数量不能超过100条' });
  }

  // 验证每个消息ID
  const invalidIds = messageIds.filter(id => !id || typeof id !== 'string' || id.trim().length === 0);
  if (invalidIds.length > 0) {
    return Promise.reject({ code: 1012, msg: '消息ID格式错误' });
  }

  const requestData = {
    Action: 'BATCH_UPDATE_MESSAGE_READ_STATUS',
    messageIds: messageIds
  };

  console.log('=== 更新消息状态为已读API请求 ===');
  console.log('请求URL: /api/user/action');
  console.log('消息IDs:', messageIds);
  console.log('请求参数:', JSON.stringify(requestData, null, 2));
  console.log('=== 更新消息状态为已读API请求结束 ===');

  return post('/api/user/action', requestData, {
    needToken: true  // 更新消息状态需要Token
  });
}

/**
 * 删除消息记录
 * @param {Array<string>} messageIds - 消息ID数组
 * @returns {Promise} Promise对象
 */
function batchDeleteMessage(messageIds) {
  // 参数验证
  if (!validateArray(messageIds)) {
    return Promise.reject({ code: 1012, msg: '消息ID数组不能为空' });
  }

  if (messageIds.length > 20) {
    return Promise.reject({ code: 1057, msg: '批量删除消息最大限制条数20' });
  }

  // 验证每个消息ID
  const invalidIds = messageIds.filter(id => !id || typeof id !== 'string' || id.trim().length === 0);
  if (invalidIds.length > 0) {
    return Promise.reject({ code: 1012, msg: '消息ID格式错误' });
  }

  const requestData = {
    Action: 'BATCH_DELETE_MESSAGE',
    messageIds: messageIds
  };

  console.log('=== 删除消息记录API请求 ===');
  console.log('请求URL: /api/user/action');
  console.log('消息IDs:', messageIds);
  console.log('请求参数:', JSON.stringify(requestData, null, 2));
  console.log('=== 删除消息记录API请求结束 ===');

  return post('/api/user/action', requestData, {
    needToken: true  // 删除消息需要Token
  });
}

module.exports = {
  sendVerificationCode,
  register,
  loginWithPassword,
  loginWithCode,
  oneKeyLogin,
  getWechatAuth,
  loginByWechatApplet,
  getAppletOpenidInfo,
  setWechatPhone,
  logout,
  resetPassword,
  getUserInfo,
  updateUserInfo,
  getUserPushSwitch,
  setUserPushSwitch,
  getUserSysMessageList,
  batchUpdateMessageReadStatus,
  batchDeleteMessage
};
