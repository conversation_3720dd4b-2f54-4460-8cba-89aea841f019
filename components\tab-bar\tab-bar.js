Component({
  properties: {
    active: {
      type: Number,
      value: 0
    }
  },
  data: {
    // 底部导航项配置
    list: [
      { text: '消息', icon: 'comment', pagePath: '/pages/message/message' },
      { text: '商城', icon: 'shopping-cart-o', pagePath: '/pages/friends/friends' },
      { text: '设备', icon: 'setting-o', pagePath: '/pages/devices/devices', customIcon: true, 
        imageUrl: '/assets/images/images/设备.png', 
        activeImageUrl: '/assets/images/images/设备-选中.png' 
      },
      { text: '云存储', icon: 'cloud-upload-o', pagePath: '/pages/cloud-storage/cloud-storage', customIcon: true, 
        imageUrl: '/assets/images/images/云存储.png',
        activeImageUrl: '/assets/images/images/云存储-选中.png'
      },
      { text: '我的', icon: 'contact', pagePath: '/pages/profile/profile' }
    ]
  },

  methods: {
    // 处理标签切换事件
    onChange(event) {
      const index = event.detail;
      const pagePath = this.data.list[index].pagePath;
      
      // 避免在当前页面重复切换
      if (index !== this.data.active) {
        wx.switchTab({
          url: pagePath
        });
      }
      
      // 触发自定义事件，供页面监听
      this.triggerEvent('change', index);
    }
  }
}) 