// utils/validator.js - 统一验证工具函数

/**
 * 验证手机号格式
 * @param {string} phone - 手机号
 * @returns {boolean} 是否为有效手机号
 */
function validatePhone(phone) {
  if (!phone || typeof phone !== 'string') {
    return false;
  }
  
  // 中国大陆手机号正则：1开头，第二位为3-9，总共11位数字
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
}

/**
 * 验证密码格式
 * @param {string} password - 密码
 * @param {Object} options - 验证选项
 * @param {number} options.minLength - 最小长度，默认6
 * @param {number} options.maxLength - 最大长度，默认20
 * @param {boolean} options.requireNumber - 是否需要数字，默认false
 * @param {boolean} options.requireLetter - 是否需要字母，默认false
 * @param {boolean} options.requireSpecial - 是否需要特殊字符，默认false
 * @returns {Object} 验证结果 {valid: boolean, message: string}
 */
function validatePassword(password, options = {}) {
  const {
    minLength = 6,
    maxLength = 20,
    requireNumber = false,
    requireLetter = false,
    requireSpecial = false
  } = options;
  
  if (!password || typeof password !== 'string') {
    return { valid: false, message: '密码不能为空' };
  }
  
  if (password.length < minLength) {
    return { valid: false, message: `密码长度不能少于${minLength}位` };
  }
  
  if (password.length > maxLength) {
    return { valid: false, message: `密码长度不能超过${maxLength}位` };
  }
  
  if (requireNumber && !/\d/.test(password)) {
    return { valid: false, message: '密码必须包含数字' };
  }
  
  if (requireLetter && !/[a-zA-Z]/.test(password)) {
    return { valid: false, message: '密码必须包含字母' };
  }
  
  if (requireSpecial && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    return { valid: false, message: '密码必须包含特殊字符' };
  }
  
  return { valid: true, message: '密码格式正确' };
}

/**
 * 验证验证码格式
 * @param {string} code - 验证码
 * @param {number} length - 验证码长度，默认6位
 * @returns {boolean} 是否为有效验证码
 */
function validateVerificationCode(code, length = 6) {
  if (!code || typeof code !== 'string') {
    return false;
  }
  
  // 验证码为纯数字，指定长度
  const codeRegex = new RegExp(`^\\d{${length}}$`);
  return codeRegex.test(code);
}

/**
 * 验证昵称格式
 * @param {string} nickname - 昵称
 * @param {Object} options - 验证选项
 * @param {number} options.minLength - 最小长度，默认1
 * @param {number} options.maxLength - 最大长度，默认16
 * @returns {Object} 验证结果 {valid: boolean, message: string}
 */
function validateNickname(nickname, options = {}) {
  const { minLength = 1, maxLength = 16 } = options;
  
  if (!nickname || typeof nickname !== 'string') {
    return { valid: false, message: '昵称不能为空' };
  }
  
  // 去除首尾空格后检查
  const trimmedNickname = nickname.trim();
  
  if (trimmedNickname.length < minLength) {
    return { valid: false, message: `昵称长度不能少于${minLength}位` };
  }
  
  if (trimmedNickname.length > maxLength) {
    return { valid: false, message: `昵称长度不能超过${maxLength}位` };
  }
  
  // 检查是否包含非法字符（可根据需要调整）
  const illegalChars = /[<>'"&]/;
  if (illegalChars.test(trimmedNickname)) {
    return { valid: false, message: '昵称包含非法字符' };
  }
  
  return { valid: true, message: '昵称格式正确' };
}

/**
 * 验证邮箱格式
 * @param {string} email - 邮箱地址
 * @returns {boolean} 是否为有效邮箱
 */
function validateEmail(email) {
  if (!email || typeof email !== 'string') {
    return false;
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * 验证URL格式
 * @param {string} url - URL地址
 * @returns {boolean} 是否为有效URL
 */
function validateUrl(url) {
  if (!url || typeof url !== 'string') {
    return false;
  }
  
  try {
    new URL(url);
    return true;
  } catch (e) {
    return false;
  }
}

/**
 * 验证身份证号格式（简单验证）
 * @param {string} idCard - 身份证号
 * @returns {boolean} 是否为有效身份证号
 */
function validateIdCard(idCard) {
  if (!idCard || typeof idCard !== 'string') {
    return false;
  }
  
  // 18位身份证号正则
  const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
  return idCardRegex.test(idCard);
}

/**
 * 验证数字范围
 * @param {number} value - 要验证的数值
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @returns {boolean} 是否在有效范围内
 */
function validateNumberRange(value, min, max) {
  if (typeof value !== 'number' || isNaN(value)) {
    return false;
  }
  
  return value >= min && value <= max;
}

/**
 * 验证数组是否非空且元素有效
 * @param {Array} array - 要验证的数组
 * @param {Function} validator - 元素验证函数（可选）
 * @returns {boolean} 是否为有效数组
 */
function validateArray(array, validator) {
  if (!Array.isArray(array) || array.length === 0) {
    return false;
  }
  
  if (typeof validator === 'function') {
    return array.every(validator);
  }
  
  return true;
}

/**
 * 验证对象是否包含必需字段
 * @param {Object} obj - 要验证的对象
 * @param {Array<string>} requiredFields - 必需字段列表
 * @returns {Object} 验证结果 {valid: boolean, message: string, missingFields: Array}
 */
function validateRequiredFields(obj, requiredFields) {
  if (!obj || typeof obj !== 'object') {
    return { 
      valid: false, 
      message: '对象不能为空', 
      missingFields: requiredFields 
    };
  }
  
  const missingFields = requiredFields.filter(field => 
    obj[field] === undefined || obj[field] === null || obj[field] === ''
  );
  
  if (missingFields.length > 0) {
    return {
      valid: false,
      message: `缺少必需字段: ${missingFields.join(', ')}`,
      missingFields
    };
  }
  
  return { valid: true, message: '所有必需字段都已提供', missingFields: [] };
}

module.exports = {
  validatePhone,
  validatePassword,
  validateVerificationCode,
  validateNickname,
  validateEmail,
  validateUrl,
  validateIdCard,
  validateNumberRange,
  validateArray,
  validateRequiredFields
};
