# 小程序 API 调用步骤文档

## 概述

本文档记录了成功调用小程序 API 的完整步骤和关键配置。

## 1. 基础配置

### 1.1 全局配置 (app.js)

```javascript
globalData: {
  // API配置信息
  baseUrl: 'https://platform.cnjabsco.com',
  appKey: '2000008',
  appSecret: '2efcf5a1fa6bacd0f727a4f1d9a2576b',
  appPackageName: 'com.applet.shankouhouse',

  // 其他配置...
}
```

### 1.2 域名配置

在微信开发者工具中：

1. 点击右上角"详情"
2. 在"本地设置"中勾选"不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书"

在微信公众平台后台：

1. 登录微信公众平台
2. 进入"开发" → "开发管理" → "开发设置"
3. 在"服务器域名"的"request 合法域名"中添加：`https://platform.cnjabsco.com`

## 2. 依赖安装

### 2.1 安装 crypto-js

```bash
npm install crypto-js
```

### 2.2 构建 npm 包

在微信开发者工具中：

1. 点击"工具" → "构建 npm"
2. 确保 crypto-js 能正常引用

## 3. 核心文件结构

```
utils/
├── crypto.js          # 加密工具类
├── request.js         # 网络请求封装
└── errorHandler.js    # 错误处理

api/
└── user.js           # 用户相关API接口

pages/
├── login/login.js    # 登录页面
└── register/register.js  # 注册页面
```

## 4. 关键实现细节

### 4.1 签名算法 (utils/crypto.js)

```javascript
function generateSignature(params) {
  const { appKey, appSecret, clientId, timestamp, nonce } = params;

  // 1. 构建签名字符串
  const signStr = `appKey=${appKey}&appSecret=${appSecret}&clientId=${clientId}&nonce=${nonce}&timestamp=${timestamp}`;

  // 2. Base64编码
  const base64Str = base64Encode(signStr);

  // 3. SHA-256签名
  const sign = sha256(base64Str);

  return {
    appKey,
    timestamp,
    nonce,
    sign,
    clientId,
  };
}
```

**关键点：**

- 时间戳使用毫秒级：`Date.now()`
- nonce 为 8 位随机字符串
- clientId 为设备唯一标识，生成后本地存储
- 必须使用 CryptoJS 库的 SHA256，不能用简化版本

### 4.2 请求格式 (utils/request.js)

```javascript
// 请求头
const headers = {
  Signature: JSON.stringify(signature),
  "Content-Type": "application/json",
};

// 请求体 - 关键：Request和Params必须作为对象，不是JSON字符串
const requestData = {
  Request: {
    requestId: generateUUID(),
    appPackageName: app.globalData.appPackageName,
    appVersion: "1.0.0",
    phoneVersion: systemInfo.system,
    phoneModel: systemInfo.model,
    appLanguage: "zh_CN",
    network: "wifi",
    country: "CN",
  },
  Params: data, // 业务参数对象
};
```

**关键点：**

- Content-Type 必须是`application/json`
- Request 和 Params 必须作为对象发送，不能是 JSON 字符串
- Signature 头部是 JSON 字符串格式
- 只在需要 Token 的接口才添加 Token 头部

### 4.3 错误处理

```javascript
// HTTP状态码处理
if (response && response.code !== undefined) {
  // 标准API响应格式
  if (response.code === 200) {
    // 成功
  } else {
    // 业务错误
  }
} else if (response && response.status !== undefined) {
  // HTTP错误响应（如400、415、500等）
}
```

## 5. 已完成功能实现

### 5.1 发送验证码 ✅

**API 接口**: `sendVerificationCode(userName, type)`

**实现文件**: `api/user.js`

```javascript
function sendVerificationCode(userName, type) {
  return post("/api/user/action", {
    Action: "SEND_VERIFICATION_CODE",
    userName: userName,
    type: type, // 1-注册 2-登录&重置密码 3-微信授权绑定手机号
  });
}
```

**页面调用示例**:

```javascript
// 发送注册验证码
sendVerificationCode(phone, 1)
  .then((response) => {
    wx.showToast({ title: "验证码已发送", icon: "success" });
  })
  .catch((error) => {
    // 错误已在errorHandler中统一处理
  });
```

### 5.2 用户注册 ✅

**API 接口**: `register(username, password, verificationCode, nickname)`

**实现文件**: `api/user.js`

```javascript
function register(username, password, verificationCode, nickname = "") {
  return post("/api/user/action", {
    Action: "REGISTER_USER",
    userName: username,
    password: md5(password), // 自动MD5加密
    verificationCode: verificationCode,
    nickname: nickname,
  });
}
```

**页面调用示例** (`pages/register/register.js`):

```javascript
performRegister: function() {
  const { phone, password, smsCode, nickname } = this.data;

  wx.showLoading({ title: '注册中...' });

  register(phone, password, smsCode, nickname)
    .then(response => {
      wx.hideLoading();
      wx.showToast({ title: '注册成功', icon: 'success' });

      // 跳转到登录页面
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    })
    .catch(error => {
      wx.hideLoading();
      // 错误已在errorHandler中统一处理
    });
}
```

### 5.3 密码登录 ✅

**API 接口**: `loginWithPassword(username, password)`

**实现文件**: `api/user.js`

```javascript
function loginWithPassword(username, password) {
  return post("/api/user/action", {
    Action: "LOGIN_PASSWORD",
    userName: username,
    password: md5(password), // 自动MD5加密
  });
}
```

**页面调用示例** (`pages/login/login.js`):

```javascript
performLogin: function() {
  const { phone, password } = this.data;

  wx.showLoading({ title: '登录中...' });

  loginWithPassword(phone, password)
    .then(response => {
      wx.hideLoading();

      // 保存登录信息
      const { token, identityId, userName } = response.data;
      wx.setStorageSync('token', token);
      wx.setStorageSync('identityId', identityId);
      wx.setStorageSync('isLoggedIn', true);

      // 更新全局状态
      app.globalData.isLoggedIn = true;
      app.globalData.userInfo = { userName, identityId };

      wx.showToast({ title: '登录成功', icon: 'success' });

      // 跳转到主页面
      setTimeout(() => {
        wx.switchTab({ url: '/pages/message/message' });
      }, 1500);
    })
    .catch(error => {
      wx.hideLoading();
      // 错误已在errorHandler中统一处理
    });
}
```

### 5.4 验证码登录 ✅

**API 接口**: `loginWithCode(username, code)`

**实现文件**: `api/user.js`

```javascript
function loginWithCode(username, code) {
  return post("/api/user/action", {
    Action: "LOGIN_VERIFICATION_CODE",
    userName: username,
    code: code,
  });
}
```

**页面调用示例** (`pages/login/login.js`):

```javascript
performSmsLogin: function() {
  const { phone, smsCode } = this.data;

  wx.showLoading({ title: '登录中...' });

  loginWithCode(phone, smsCode)
    .then(response => {
      wx.hideLoading();

      // 保存登录信息
      const { token, identityId, userName } = response.data;
      wx.setStorageSync('token', token);
      wx.setStorageSync('identityId', identityId);
      wx.setStorageSync('isLoggedIn', true);

      // 更新全局状态
      app.globalData.isLoggedIn = true;
      app.globalData.userInfo = { userName, identityId };

      wx.showToast({ title: '登录成功', icon: 'success' });

      // 跳转到主页面
      setTimeout(() => {
        wx.switchTab({ url: '/pages/message/message' });
      }, 1500);
    })
    .catch(error => {
      wx.hideLoading();
      // 错误已在errorHandler中统一处理
    });
}
```

## 6. 微信授权登录接口

### 6.1 微信授权码操作 ✅

```javascript
getWechatAuth(code);
```

### 6.2 小程序微信账号登录 ✅

```javascript
loginByWechatApplet(mobileCode, openIdCode);
```

### 6.3 获取微信小程序用户 openId 信息 ✅

```javascript
getAppletOpenidInfo(code, productKey, deviceName, nickName);
```

### 6.4 微信 OpenID 绑定手机号 ✅

```javascript
setWechatPhone(userName, openId, verificationCode);
```

## 7. 调试过程记录

### 7.1 常见错误及解决方案

| 错误码   | 错误信息                | 原因              | 解决方案                                   |
| -------- | ----------------------- | ----------------- | ------------------------------------------ |
| 400      | Bad Request             | 请求格式错误      | 检查 Request/Params 格式，确保作为对象发送 |
| 415      | Unsupported Media Type  | Content-Type 错误 | 使用 application/json                      |
| 域名限制 | url not in domain list  | 域名未配置        | 配置合法域名或开启调试模式                 |
| 1005     | verification code error | 验证码错误或超时  | 重新获取验证码                             |
| 1008     | password error          | 密码错误          | 检查密码是否正确                           |
| 1014     | sign check error        | 签名校验失败      | 检查签名算法实现                           |

### 7.2 成功标志

- HTTP 状态码：200
- 响应格式：`{code: 200, msg: "success", data: ...}` 或业务错误码

## 8. 完整调用流程

### 8.1 用户注册流程

```
1. 用户输入手机号 → 发送验证码(type=1)
2. 用户输入密码、验证码、昵称 → 调用注册接口
3. 注册成功 → 跳转到登录页面
```

### 8.2 密码登录流程

```
1. 用户输入手机号、密码 → 调用密码登录接口
2. 登录成功 → 保存token和用户信息 → 跳转到主页面
```

### 8.3 验证码登录流程

```
1. 用户输入手机号 → 发送验证码(type=2)
2. 用户输入验证码 → 调用验证码登录接口
3. 登录成功 → 保存token和用户信息 → 跳转到主页面
```

## 9. 注意事项

1. **crypto-js 库必须正确加载**，否则签名会失败
2. **时间戳使用毫秒级**，不是秒级
3. **Request 和 Params 作为对象发送**，这是关键点
4. **签名算法严格按照文档执行**：字符串拼接 → Base64 编码 → SHA256 签名
5. **clientId 需要持久化存储**，每个设备唯一
6. **错误处理要区分 HTTP 错误和业务错误**
7. **密码自动 MD5 加密**，在 API 层面处理，页面无需关心
8. **登录成功后必须保存 token 和用户信息**到本地存储和全局状态

## 10. 测试验证

### 10.1 功能测试清单

- [x] 发送验证码（注册）
- [x] 发送验证码（登录）
- [x] 用户注册
- [x] 密码登录
- [x] 验证码登录
- [x] 微信授权登录相关接口
- [ ] 一键登录（待实现）

### 10.2 成功调用的标志

- 控制台无网络错误
- 收到 HTTP 200 响应
- 响应包含 code 字段（200 为成功，其他为业务错误）
- 登录成功后能正确保存用户信息
- 页面跳转正常

## 11. 代码问题清单

### 11.1 重复代码问题

#### 问题描述

在 `utils/errorHandler.js` 和 `utils/auth.js` 中存在重复的 Token 错误处理逻辑：

**utils/errorHandler.js**:

```javascript
// 需要清除登录状态的错误码
const TOKEN_ERROR_CODES = [1013, 1028, 1049];

function handleTokenExpired(callback) {
  // 清除登录状态逻辑...
}
```

**utils/auth.js**:

```javascript
function isTokenError(error) {
  // Token错误码：1013-Token错误，1028-Token过期，1049-Token无效
  return [1013, 1028, 1049].includes(error.code);
}

function handleTokenError(message = "登录已过期，请重新登录") {
  // 类似的清除登录状态逻辑...
}
```

**解决方案**: 统一使用 `utils/errorHandler.js` 处理 Token 错误，删除 `utils/auth.js` 中的重复代码。

### 11.2 命名不一致问题

#### 问题描述

Token 错误处理函数命名不统一：

- `utils/errorHandler.js` 中使用 `handleTokenExpired`
- `utils/auth.js` 中使用 `handleTokenError`

**解决方案**: 统一使用 `handleTokenExpired` 命名。

### 11.3 错误边界处理问题

#### 问题描述

在 `utils/errorHandler.js` 的 `processResponse` 函数中，对于未知响应格式的处理过于宽松：

```javascript
// 无法识别的响应格式，视为成功
console.warn("无法识别的响应格式:", response);
resolve({
  success: true,
  code: 200,
  msg: "成功",
  data: response,
});
```

**解决方案**: 对未知格式应该更谨慎处理，建议抛出错误：

```javascript
// 无法识别的响应格式，抛出错误
console.error("无法识别的响应格式:", response);
reject({
  code: -1,
  msg: "响应格式错误",
  data: response,
});
```

### 11.4 输入验证缺失问题

#### 问题描述

在 `api/user.js` 中的 API 函数缺少参数验证：

```javascript
function sendVerificationCode(userName, type) {
  // 直接使用参数，没有验证 userName 和 type 的有效性
  const requestData = {
    Action: 'SEND_VERIFICATION_CODE',
    userName: userName,
    type: type
  };
```

**解决方案**: 添加参数验证：

```javascript
function sendVerificationCode(userName, type) {
  // 参数验证
  if (!userName || typeof userName !== 'string') {
    return Promise.reject({ code: 1000, msg: '用户名格式错误' });
  }

  if (!type || ![1, 2, 3].includes(type)) {
    return Promise.reject({ code: 1020, msg: '验证码类型错误' });
  }

  const requestData = {
    Action: 'SEND_VERIFICATION_CODE',
    userName: userName,
    type: type
  };
```

### 11.5 手机号验证逻辑不一致

#### 问题描述

在登录页面中存在多个手机号验证函数，使用了不同的正则表达式和逻辑。

**解决方案**: 创建统一的手机号验证工具函数：

```javascript
// utils/validator.js
function validatePhone(phone) {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
}
```

### 11.6 潜在内存泄漏问题

#### 问题描述

在错误处理中使用了 `setTimeout`，但没有清理机制：

```javascript
setTimeout(() => {
  wx.reLaunch({
    url: "/pages/login/login",
    complete: () => {
      if (typeof callback === "function") {
        callback();
      }
    },
  });
}, 300);
```

**解决方案**: 考虑在页面卸载时清理定时器，或使用更安全的异步处理方式。

### 11.7 修复优先级

| 优先级 | 问题类型         | 影响程度     | 修复难度 |
| ------ | ---------------- | ------------ | -------- |
| 高     | 重复代码问题     | 维护困难     | 中等     |
| 高     | 错误边界处理     | 可能导致异常 | 低       |
| 中     | 命名不一致       | 代码可读性   | 低       |
| 中     | 输入验证缺失     | 安全性       | 中等     |
| 低     | 手机号验证不一致 | 用户体验     | 低       |
| 低     | 潜在内存泄漏     | 性能影响     | 中等     |

### 11.8 修复计划

1. **第一阶段**：修复重复代码和错误边界处理问题 ✅
2. **第二阶段**：统一命名规范和添加输入验证 ✅
3. **第三阶段**：优化手机号验证和内存管理 ✅

### 11.9 修复完成情况

#### 已修复问题 ✅

1. **重复代码问题** - 已修复

   - 删除了 `utils/auth.js` 中的重复 Token 错误处理代码
   - 统一使用 `utils/errorHandler.js` 处理 Token 错误
   - 使用统一的 `TOKEN_ERROR_CODES` 常量

2. **错误边界处理问题** - 已修复

   - 修改了 `utils/errorHandler.js` 中的 `processResponse` 函数
   - 对未知响应格式进行更严格的处理，抛出错误而不是默认成功

3. **输入验证缺失问题** - 已修复

   - 创建了 `utils/validator.js` 统一验证工具
   - 为 `api/user.js` 中的所有 API 函数添加了参数验证
   - 包括手机号、密码、验证码、昵称等验证

4. **手机号验证不一致问题** - 已修复

   - 创建了统一的 `validatePhone` 函数
   - 所有 API 函数使用相同的手机号验证逻辑

5. **潜在内存泄漏问题** - 已修复

   - 优化了 `handleTokenExpired` 函数中的 `setTimeout` 使用
   - 添加了定时器管理和清理机制
   - 提供了 `clearTokenExpiredTimer` 函数用于资源清理

6. **命名不一致问题** - 已修复
   - 统一使用 `handleTokenExpired` 命名
   - 删除了重复的 `handleTokenError` 函数

#### 新增功能

1. **utils/validator.js** - 统一验证工具

   - `validatePhone()` - 手机号验证
   - `validatePassword()` - 密码验证（支持复杂规则）
   - `validateVerificationCode()` - 验证码验证
   - `validateNickname()` - 昵称验证
   - `validateEmail()` - 邮箱验证
   - `validateUrl()` - URL 验证
   - `validateIdCard()` - 身份证验证
   - `validateNumberRange()` - 数字范围验证
   - `validateArray()` - 数组验证
   - `validateRequiredFields()` - 必需字段验证

2. **增强的错误处理**
   - 更严格的响应格式验证
   - 改进的内存管理
   - 统一的 Token 错误处理

#### 代码质量提升

- **可维护性**：消除了重复代码，统一了处理逻辑
- **安全性**：添加了全面的输入验证
- **稳定性**：改进了错误边界处理和内存管理
- **一致性**：统一了命名规范和验证逻辑

---

## 12. 参数验证完善情况

### 12.1 已完成的参数验证 ✅

所有 18 个用户相关接口已全部添加参数验证，具体如下：

#### 基础用户接口（已验证）

1. **sendVerificationCode** - 发送验证码

   - 手机号格式验证
   - 验证码类型验证（1-3）

2. **register** - 用户注册

   - 手机号格式验证
   - 密码强度验证（6-20 位）
   - 验证码格式验证
   - 昵称格式验证（可选）

3. **loginWithPassword** - 密码登录

   - 手机号格式验证
   - 密码非空验证

4. **loginWithCode** - 验证码登录

   - 手机号格式验证
   - 验证码格式验证

5. **oneKeyLogin** - 一键登录

   - accessToken 非空验证

6. **resetPassword** - 重置密码
   - 手机号格式验证
   - 密码强度验证（6-20 位）
   - 验证码格式验证

#### 微信相关接口（已验证）

7. **getWechatAuth** - 微信授权码操作

   - 微信授权码非空验证

8. **loginByWechatApplet** - 小程序微信账号登录

   - mobileCode 非空验证
   - openIdCode 格式验证（可选）

9. **getAppletOpenidInfo** - 获取微信小程序用户 openId 信息

   - 微信授权码非空验证
   - productKey 非空验证
   - deviceName 非空验证
   - nickName 格式验证（可选）

10. **setWechatPhone** - 微信 OpenID 绑定手机号
    - 手机号格式验证
    - openId 非空验证
    - 验证码格式验证

#### 用户信息接口（已验证）

11. **logout** - 用户退出登录

    - 无需参数验证（已添加标记）

12. **getUserInfo** - 获取用户信息

    - 无需参数验证（已添加标记）

13. **updateUserInfo** - 更新用户信息
    - 用户信息对象非空验证
    - 昵称格式验证（可选）
    - 头像 URL 格式验证（可选）
    - 性别参数验证（可选）

#### 推送设置接口（已验证）

14. **getUserPushSwitch** - 查询用户推送开关

    - 无需参数验证（已添加标记）

15. **setUserPushSwitch** - 设置用户推送开关
    - 布尔值类型验证

#### 消息管理接口（已验证）

16. **getUserSysMessageList** - 查询用户消息列表

    - 页码范围验证（1-10000）
    - 每页条数验证（1-100）
    - 读取状态验证（0-2）

17. **batchUpdateMessageReadStatus** - 更新消息状态为已读

    - 消息 ID 数组非空验证
    - 批量操作数量限制（≤100 条）
    - 消息 ID 格式验证

18. **batchDeleteMessage** - 删除消息记录
    - 消息 ID 数组非空验证
    - 批量删除数量限制（≤20 条）
    - 消息 ID 格式验证

### 12.2 验证规则统计

| 验证类型     | 使用次数 | 相关接口                               |
| ------------ | -------- | -------------------------------------- |
| 手机号验证   | 6        | 注册、登录、重置密码、绑定手机号等     |
| 密码验证     | 2        | 注册、重置密码                         |
| 验证码验证   | 4        | 注册、验证码登录、重置密码、绑定手机号 |
| 昵称验证     | 2        | 注册、更新用户信息                     |
| 数组验证     | 2        | 批量消息操作                           |
| URL 验证     | 1        | 更新用户信息（头像）                   |
| 数值范围验证 | 3        | 消息列表查询、验证码类型               |
| 非空验证     | 8        | 各种必需参数                           |

### 12.3 验证效果

- **安全性提升**：所有输入参数都经过严格验证，防止无效请求
- **用户体验**：提前拦截错误输入，减少网络请求
- **代码质量**：统一的验证逻辑，易于维护
- **错误处理**：明确的错误码和错误信息

---

**更新时间：** 2025-01-22
**状态：** 所有问题已修复完成 ✅，用户接口参数验证 100%完成 ✅
