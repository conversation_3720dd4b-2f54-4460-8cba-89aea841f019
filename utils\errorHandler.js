// utils/errorHandler.js - 错误处理工具类

/**
 * 状态码对应的错误信息
 */
const ERROR_MESSAGES = {
  200: '成功',
  400: '请求失败',
  401: '腾讯SDK异常',
  1000: '用户名格式错误',
  1001: '用户名已存在',
  1002: '微信授权失败',
  1003: '微信APP没有配置',
  1004: '验证码发送失败',
  1005: '验证码错误或超时',
  1006: '验证码超时',
  1007: '用户不存在',
  1008: '密码错误',
  1009: '密码为空',
  1010: '账号异常',
  1011: '账号锁定-密码连续6次输入错误',
  1012: '参数错误',
  1013: 'Token错误',
  1014: '签名校验失败',
  1015: 'Header参数不完整',
  1016: '用户已绑定微信',
  1017: '微信OpenId无效',
  1018: '服务器异常',
  1019: '验证码发送频繁限制',
  1020: '验证码类型错误',
  1021: '手机号已绑定其他微信',
  1022: '头像上传失败',
  1023: '图片只支持png或jpg格式',
  1024: 'IdentityId错误',
  1025: '微信已绑定其他用户',
  1026: '一键登录token验证失败',
  1027: '用户没有设置密码',
  1028: '用户token过期',
  1029: 'Action错误',
  1030: 'Request信息为空',
  1031: '分组ID异常',
  1032: '分组名称异常',
  1033: '分组已经存在',
  1034: '设备ID异常',
  1035: '设备离线',
  1036: '设备已经被自己添加',
  1037: '设备已经被别人添加',
  1038: '设备分享已达上限',
  1039: '设备ID不支持',
  1040: '设备昵称过长',
  1041: '设备未绑定',
  1042: '没有设备权限',
  1043: '二维码超时',
  1044: '设备已经分享给该用户',
  1045: '推送手机类型不支持',
  1046: '用户昵称最长16位',
  1047: '好友已存在',
  1048: '好友不存在',
  1049: '用户TOKEN信息无效',
  1050: '正在转移记录不能删除',
  1051: '用户名是自己',
  1052: '用户名下有绑定的设备',
  1053: '有分享给好友设备',
  1054: '两次密码相等',
  1055: '获取TTS信息失败',
  1056: 'SN码不存在',
  1057: '批量删除设备报警消息最大限制条数20',
  1058: '验证码发送失败',
  1059: '工具升级配置不存在',
  1061: '获取openid失败'
};

// 需要清除登录状态的错误码
const TOKEN_ERROR_CODES = [1013, 1028, 1049];

/**
 * 处理API错误
 * @param {number} code - 错误码
 * @param {string} defaultMessage - 默认错误信息
 * @param {boolean} showToast - 是否显示Toast提示
 * @returns {string} 错误信息
 */
function handleError(code, defaultMessage = '请求失败', showToast = true) {
  const message = ERROR_MESSAGES[code] || defaultMessage;
  
  if (showToast) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  }
  
  console.error(`API Error [${code}]: ${message}`);

  // 如果是Token相关错误，自动处理Token
  if (TOKEN_ERROR_CODES.includes(code)) {
    handleTokenExpired();
  }
  
  return message;
}

/**
 * 处理网络错误
 * @param {Object} error - 错误对象
 * @param {boolean} showToast - 是否显示Toast提示
 */
function handleNetworkError(error, showToast = true) {
  console.error('Network Error:', error);
  
  let message = '网络请求失败，请检查网络连接';
  
  if (error) {
    if (error.errMsg) {
      if (error.errMsg.includes('timeout')) {
        message = '请求超时，请稍后再试';
      } else if (error.errMsg.includes('fail')) {
        message = '网络连接失败，请检查网络';
      }
    }
  }
  
  if (showToast) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  }
  
  return {
    code: -100,
    msg: message,
    error
  };
}

// 用于存储定时器ID，便于清理
let tokenExpiredTimer = null;

/**
 * 处理Token过期
 * @param {Function} callback - 回调函数
 */
function handleTokenExpired(callback) {
  console.log('=== Token已过期，准备重新登录 ===');

  // 清理之前的定时器
  if (tokenExpiredTimer) {
    clearTimeout(tokenExpiredTimer);
    tokenExpiredTimer = null;
  }

  // 清除登录状态
  const app = getApp();
  if (app && app.globalData) {
    app.globalData.userInfo = null;
    app.globalData.isLoggedIn = false;
  }

  try {
    wx.removeStorageSync('userInfo');
    wx.removeStorageSync('isLoggedIn');
    wx.removeStorageSync('token');
    wx.removeStorageSync('identityId');
  } catch (e) {
    console.error('清除登录状态失败', e);
  }

  // 显示登录过期提示
  wx.showModal({
    title: '登录已过期',
    content: '您的登录已过期，请重新登录',
    showCancel: false,
    success: () => {
      // 使用Promise和微任务来避免setTimeout
      Promise.resolve().then(() => {
        // 延迟跳转，确保提示框已关闭
        tokenExpiredTimer = setTimeout(() => {
          tokenExpiredTimer = null;
          wx.reLaunch({
            url: '/pages/login/login',
            complete: () => {
              if (typeof callback === 'function') {
                callback();
              }
            }
          });
        }, 300);
      });
    },
    fail: () => {
      // 如果模态框显示失败，直接跳转
      wx.reLaunch({
        url: '/pages/login/login',
        complete: () => {
          if (typeof callback === 'function') {
            callback();
          }
        }
      });
    }
  });

  return true;
}

/**
 * 清理定时器资源
 */
function clearTokenExpiredTimer() {
  if (tokenExpiredTimer) {
    clearTimeout(tokenExpiredTimer);
    tokenExpiredTimer = null;
  }
}

/**
 * 处理API响应
 * @param {Object} response - API响应对象
 * @param {boolean} showToast - 是否显示Toast提示
 * @returns {Promise} Promise对象
 */
function processResponse(response, showToast = true) {
  return new Promise((resolve, reject) => {
    // 检查响应格式
    if (!response) {
      const error = { code: -1, msg: '服务器响应为空' };
      if (showToast) handleError(-1, error.msg);
      reject(error);
      return;
    }
    
    // 处理标准响应格式 {status_code, message, data}
    if (response.hasOwnProperty('status_code')) {
      const code = response.status_code;
      const message = response.message || '未知错误';
      const data = response.data || null;
      
      if (code === 200) {
        // 成功响应
        resolve({
          success: true,
          code,
          msg: message,
          data
        });
      } else {
        // 检查是否是Token相关错误
        if (TOKEN_ERROR_CODES.includes(code)) {
          handleTokenExpired();
        }
        
        // 错误响应
        const error = { code, msg: message, data };
        if (showToast) handleError(code, message);
        reject(error);
      }
      return;
    }
    
    // 处理简化响应格式 {code, msg, data}
    if (response.hasOwnProperty('code')) {
      const code = response.code;
      const message = response.msg || response.message || ERROR_MESSAGES[code] || '未知错误';
      const data = response.data || null;
      
      if (code === 200 || code === 0) {
        // 成功响应
        resolve({
          success: true,
          code,
          msg: message,
          data
        });
      } else {
        // 检查是否是Token相关错误
        if (TOKEN_ERROR_CODES.includes(code)) {
          handleTokenExpired();
        }
        
        // 错误响应
        const error = { code, msg: message, data };
        if (showToast) handleError(code, message);
        reject(error);
      }
      return;
    }
    
    // 未知响应格式，尝试解析
    if (typeof response === 'object') {
      // 如果有明显的成功标志
      if (response.success === true || response.Success === true) {
        resolve({
          success: true,
          code: 200,
          msg: '成功',
          data: response.data || response.Data || response
        });
        return;
      }
      
      // 如果有明显的错误标志
      if (response.success === false || response.Success === false) {
        const error = {
          code: response.code || response.Code || -1,
          msg: response.msg || response.message || response.Message || '请求失败',
          data: response.data || response.Data || null
        };
        if (showToast) handleError(error.code, error.msg);
        reject(error);
        return;
      }
    }
    
    // 无法识别的响应格式，抛出错误
    console.error('无法识别的响应格式:', response);
    const error = {
      code: -1,
      msg: '响应格式错误',
      data: response
    };
    if (showToast) handleError(-1, error.msg);
    reject(error);
  });
}

module.exports = {
  handleError,
  handleNetworkError,
  handleTokenExpired,
  clearTokenExpiredTimer,
  processResponse,
  ERROR_MESSAGES,
  TOKEN_ERROR_CODES
};
