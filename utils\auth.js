// utils/auth.js - 认证相关工具函数
const { handleTokenExpired, TOKEN_ERROR_CODES } = require('./errorHandler.js');

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
function isLoggedIn() {
  try {
    const isLoggedIn = wx.getStorageSync('isLoggedIn');
    const token = wx.getStorageSync('token');
    return !!(isLoggedIn && token);
  } catch (e) {
    console.error('检查登录状态失败', e);
    return false;
  }
}

/**
 * 获取当前用户Token
 * @returns {string|null} Token或null
 */
function getToken() {
  try {
    return wx.getStorageSync('token');
  } catch (e) {
    console.error('获取Token失败', e);
    return null;
  }
}

/**
 * 获取当前用户信息
 * @returns {Object|null} 用户信息或null
 */
function getUserInfo() {
  try {
    return wx.getStorageSync('userInfo');
  } catch (e) {
    console.error('获取用户信息失败', e);
    return null;
  }
}

/**
 * 保存登录信息
 * @param {Object} loginData - 登录数据
 * @param {string} loginData.token - Token
 * @param {string} loginData.identityId - 用户ID
 * @param {string} loginData.userName - 用户名
 * @returns {boolean} 是否保存成功
 */
function saveLoginInfo(loginData) {
  try {
    const { token, identityId, userName } = loginData;
    
    wx.setStorageSync('token', token);
    wx.setStorageSync('identityId', identityId);
    wx.setStorageSync('isLoggedIn', true);
    
    // 更新全局状态
    const app = getApp();
    if (app.globalData) {
      app.globalData.isLoggedIn = true;
      app.globalData.userInfo = {
        userName: userName,
        identityId: identityId
      };
    }
    
    console.log('=== 登录信息保存成功 ===');
    console.log('Token:', token);
    console.log('IdentityId:', identityId);
    console.log('UserName:', userName);
    console.log('=== 登录信息保存成功结束 ===');
    
    return true;
  } catch (e) {
    console.error('保存登录信息失败', e);
    return false;
  }
}

/**
 * 清除登录信息
 * @returns {boolean} 是否清除成功
 */
function clearLoginInfo() {
  try {
    // 清除存储
    wx.removeStorageSync('token');
    wx.removeStorageSync('identityId');
    wx.removeStorageSync('userInfo');
    wx.removeStorageSync('isLoggedIn');
    
    // 更新全局状态
    const app = getApp();
    if (app.globalData) {
      app.globalData.isLoggedIn = false;
      app.globalData.userInfo = null;
    }
    
    console.log('=== 登录信息清除成功 ===');
    return true;
  } catch (e) {
    console.error('清除登录信息失败', e);
    return false;
  }
}

/**
 * 判断是否是Token错误
 * @param {Object} error - 错误对象
 * @returns {boolean} 是否是Token错误
 */
function isTokenError(error) {
  if (!error || !error.code) return false;

  // 使用统一的Token错误码常量
  return TOKEN_ERROR_CODES.includes(error.code);
}

/**
 * 处理API错误，特别处理Token错误
 * @param {Object} error - 错误对象
 * @param {string} defaultMessage - 默认错误信息
 * @returns {boolean} 是否是Token错误
 */
function handleApiError(error, defaultMessage = '请求失败，请重试') {
  console.log('=== 处理API错误 ===');
  console.log('错误对象:', error);

  if (!error) {
    console.log('错误对象为空，显示默认信息');
    wx.showToast({
      title: defaultMessage,
      icon: 'none'
    });
    return false;
  }

  // 检查是否是Token错误
  if (isTokenError(error)) {
    console.log('检测到Token错误:', error.code);

    // 使用统一的Token错误处理函数
    handleTokenExpired();
    return true;
  }

  // 其他API错误，显示具体错误信息
  const message = error.msg || error.message || defaultMessage;
  wx.showToast({
    title: message,
    icon: 'none',
    duration: 3000
  });

  return false;
}

module.exports = {
  isLoggedIn,
  getToken,
  getUserInfo,
  saveLoginInfo,
  clearLoginInfo,
  isTokenError,
  handleApiError
};
